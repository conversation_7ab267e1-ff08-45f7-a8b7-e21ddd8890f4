\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand*\HyPL@Entry[1]{}
\citation{ke2011review}
\citation{gougeon1998automatic}
\citation{culvenor2002tida,erikson2003segmentation}
\citation{jing2012automated,yang2014automated}
\citation{wang2004automated,wang2010crown,lamar2005automated,tong2021improved}
\citation{zhao2023review}
\citation{lassalle2022cnn}
\citation{freudenberg2022individual}
\citation{ronneberger2015u}
\citation{he2017mask}
\citation{braga2020amazon,hao2021individual,ball2023detectree2}
\citation{braga2020amazon}
\citation{hao2021individual}
\citation{ball2023detectree2}
\citation{getzin2022nat}
\citation{rsegnet2023}
\citation{geometric2023}
\citation{dtw2023}
\citation{sida2023}
\citation{meta2024}
\HyPL@Entry{0<</S/D>>}
\@writefile{toc}{\contentsline {section}{\numberline {I}引言}{1}{section.1}\protected@file@percent }
\citation{rgb2024}
\citation{clip2023}
\citation{gat2022}
\citation{priyadarshan2017biology}
\@writefile{toc}{\contentsline {section}{\numberline {II}材料与方法}{2}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}研究区域}{2}{subsection.2.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces 研究区域位置图。}}{2}{figure.1}\protected@file@percent }
\newlabel{fig:study_area}{{1}{2}{研究区域位置图。}{figure.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}数据集构建}{2}{subsection.2.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces 橡胶树数据集物候期分布}}{3}{table.1}\protected@file@percent }
\newlabel{tab:phenology}{{I}{3}{橡胶树数据集物候期分布}{table.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{3}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}CSAF框架总体设计}{3}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}GM-Mamba边界增强模块}{3}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces CSAF框架总体架构图。(A) 预处理模块包括随机裁剪、亮度/对比度调整和骨干网络特征提取。(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界。(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题。(D) MPC-Poisson模块通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰。(E) 输出精确的树冠掩膜。}}{3}{figure.2}\protected@file@percent }
\newlabel{fig:framework}{{2}{3}{CSAF框架总体架构图。(A) 预处理模块包括随机裁剪、亮度/对比度调整和骨干网络特征提取。(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界。(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题。(D) MPC-Poisson模块通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰。(E) 输出精确的树冠掩膜。}{figure.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}MPC-Poisson物理约束模块}{4}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces GM-Mamba边界感知树冠分割模块示意图。该模块集成了使用傅里叶变换的频域去噪、通过拉普拉斯金字塔的多尺度边缘增强，以及使用基于Mamba的状态空间模型的长程依赖建模。从频域的实部和虚部分量中提取双尺度梯度线索，实现从粗到细的边缘检测。输出传递到选择性扫描增强的SSM中，通过动态状态更新捕获方向性树冠结构。融合输出增强了边界连续性和树冠级空间一致性。}}{5}{figure.3}\protected@file@percent }
\newlabel{fig:gm_mamba}{{3}{5}{GM-Mamba边界感知树冠分割模块示意图。该模块集成了使用傅里叶变换的频域去噪、通过拉普拉斯金字塔的多尺度边缘增强，以及使用基于Mamba的状态空间模型的长程依赖建模。从频域的实部和虚部分量中提取双尺度梯度线索，实现从粗到细的边缘检测。输出传递到选择性扫描增强的SSM中，通过动态状态更新捕获方向性树冠结构。融合输出增强了边界连续性和树冠级空间一致性。}{figure.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}MASA-Optimizer持续学习机制}{5}{subsection.3.4}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces MPC-Poisson模块在树冠分割过程中强制执行形态约束的架构图。该模块通过四层MLP将归一化空间坐标映射，产生树冠存在的概率场$\gamma (x,y)$。从泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}}{6}{figure.4}\protected@file@percent }
\newlabel{fig:mpc_poisson}{{4}{6}{MPC-Poisson模块在树冠分割过程中强制执行形态约束的架构图。该模块通过四层MLP将归一化空间坐标映射，产生树冠存在的概率场$\gamma (x,y)$。从泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}{figure.4}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces MASA-Optimizer三阶段优化算法}}{6}{algorithm.1}\protected@file@percent }
\newlabel{alg:masa}{{1}{6}{MASA-Optimizer持续学习机制}{algorithm.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experimental Results}{7}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}实验环境}{7}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}评价指标}{7}{subsection.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}消融实验}{7}{subsection.4.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces MASA-Optimizer持续学习模块架构图。该模块采用三阶段多智能体协作策略解决跨物候期的灾难性遗忘问题：初期阶段使用模拟退火进行全局探索，中期阶段通过Q-learning学习最优调整策略，后期阶段采用遗传算法进行局部精调。经验回放机制通过时相变异度量选择性地回放历史样本，动态调节遗忘因子$\alpha $以平衡新旧知识的融合比例。}}{8}{figure.5}\protected@file@percent }
\newlabel{fig:masa_optimizer}{{5}{8}{MASA-Optimizer持续学习模块架构图。该模块采用三阶段多智能体协作策略解决跨物候期的灾难性遗忘问题：初期阶段使用模拟退火进行全局探索，中期阶段通过Q-learning学习最优调整策略，后期阶段采用遗传算法进行局部精调。经验回放机制通过时相变异度量选择性地回放历史样本，动态调节遗忘因子$\alpha $以平衡新旧知识的融合比例。}{figure.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}优化算法有效性分析}{8}{subsection.4.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-E}}GM-Mamba模块组件有效性分析}{8}{subsection.4.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces 消融实验可视化结果。展示了不同模块组合在典型挑战场景下的分割效果：(a)原始图像，(b)真实标注，(c)基线模型，(d)GM-Mamba，(e)MASA-Optimizer，(f)MPC-Poisson，(g)CSAF完整框架。第一行：密集冠层边界模糊场景；第二行：跨物候期特征变化场景；第三行：非目标植被干扰场景。}}{9}{figure.6}\protected@file@percent }
\newlabel{fig:ablation_visual}{{6}{9}{消融实验可视化结果。展示了不同模块组合在典型挑战场景下的分割效果：(a)原始图像，(b)真实标注，(c)基线模型，(d)GM-Mamba，(e)MASA-Optimizer，(f)MPC-Poisson，(g)CSAF完整框架。第一行：密集冠层边界模糊场景；第二行：跨物候期特征变化场景；第三行：非目标植被干扰场景。}{figure.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces CSAF及其子模块的消融实验结果}}{10}{table.2}\protected@file@percent }
\newlabel{tab:ablation}{{II}{10}{CSAF及其子模块的消融实验结果}{table.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces 三阶段优化算法组合有效性实验结果}}{10}{table.3}\protected@file@percent }
\newlabel{tab:optimization_order}{{III}{10}{三阶段优化算法组合有效性实验结果}{table.3}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces GM-Mamba模块组件替换实验结果}}{10}{table.4}\protected@file@percent }
\newlabel{tab:gm_mamba_components}{{IV}{10}{GM-Mamba模块组件替换实验结果}{table.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-F}}MPC-Poisson模块组件有效性分析}{10}{subsection.4.6}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces MPC-Poisson模块组件消融实验结果}}{11}{table.5}\protected@file@percent }
\newlabel{tab:mpc_poisson_components}{{V}{11}{MPC-Poisson模块组件消融实验结果}{table.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-G}}在RT-Set数据集上的对比实验}{11}{subsection.4.7}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-H}}在BT-Set数据集上的对比实验}{11}{subsection.4.8}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-I}}在UT-Set数据集上的对比实验}{11}{subsection.4.9}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {VI}{\ignorespaces RT-Set数据集上的性能对比}}{12}{table.6}\protected@file@percent }
\newlabel{tab:rt_set_comparison}{{VI}{12}{RT-Set数据集上的性能对比}{table.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VII}{\ignorespaces BT-Set数据集上的性能对比}}{12}{table.7}\protected@file@percent }
\newlabel{tab:bt_set_comparison}{{VII}{12}{BT-Set数据集上的性能对比}{table.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-J}}在CTF-Set数据集上的泛化实验}{12}{subsection.4.10}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}讨论}{12}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-A}}橡胶树冠计数方法与精度分析}{12}{subsection.5.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {VIII}{\ignorespaces UT-Set数据集上的性能对比}}{13}{table.8}\protected@file@percent }
\newlabel{tab:ut_set_comparison}{{VIII}{13}{UT-Set数据集上的性能对比}{table.8}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IX}{\ignorespaces CTF-Set数据集上的泛化性能对比}}{13}{table.9}\protected@file@percent }
\newlabel{tab:ctf_set_generalization}{{IX}{13}{CTF-Set数据集上的泛化性能对比}{table.9}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}1}单一林分计数性能}{13}{subsubsection.5.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}2}混交林计数性能}{13}{subsubsection.5.1.2}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{ieee_tgrs_references}
\bibcite{ke2011review}{1}
\bibcite{gougeon1998automatic}{2}
\bibcite{culvenor2002tida}{3}
\bibcite{erikson2003segmentation}{4}
\bibcite{jing2012automated}{5}
\bibcite{yang2014automated}{6}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}3}计数误差分析}{14}{subsubsection.5.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-B}}方法局限性与改进方向}{14}{subsection.5.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {VI}结论}{14}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{14}{section*.2}\protected@file@percent }
\bibcite{wang2004automated}{7}
\bibcite{wang2010crown}{8}
\bibcite{lamar2005automated}{9}
\bibcite{tong2021improved}{10}
\bibcite{zhao2023review}{11}
\bibcite{lassalle2022cnn}{12}
\bibcite{freudenberg2022individual}{13}
\bibcite{ronneberger2015u}{14}
\bibcite{he2017mask}{15}
\bibcite{braga2020amazon}{16}
\bibcite{hao2021individual}{17}
\bibcite{ball2023detectree2}{18}
\bibcite{priyadarshan2017biology}{19}
\gdef \@abspage@last{15}
