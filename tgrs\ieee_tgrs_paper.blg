This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: ieee_tgrs_paper.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: IEEEtran.bst
Reallocating 'name_of_file' (item size: 1) to 21 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Database file #1: ieee_tgrs_references.bib
Warning--I didn't find a database entry for "getzin2022nat"
Warning--I didn't find a database entry for "rsegnet2023"
Warning--I didn't find a database entry for "geometric2023"
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 19 entries,
            4087 wiz_defined-function locations,
            950 strings with 12064 characters,
and the built_in function-call counts, 17144 in all, are:
= -- 1402
> -- 462
< -- 169
+ -- 261
- -- 86
* -- 847
:= -- 2479
add.period$ -- 39
call.type$ -- 19
change.case$ -- 18
chr.to.int$ -- 381
cite$ -- 19
duplicate$ -- 1181
empty$ -- 1242
format.name$ -- 95
if$ -- 3997
int.to.chr$ -- 0
int.to.str$ -- 19
missing$ -- 202
newline$ -- 80
num.names$ -- 19
pop$ -- 437
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 1315
stack$ -- 0
substring$ -- 938
swap$ -- 1051
text.length$ -- 42
text.prefix$ -- 0
top$ -- 5
type$ -- 19
warning$ -- 0
while$ -- 81
width$ -- 21
write$ -- 215
(There were 3 warnings)
